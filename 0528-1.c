
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

#define NX  30     // 空間分割数
#define NT  400   // 時間ステップ数
#define L   1.0     // 空間長さ
#define T   1.0     // 終了時間
#define D   0.1   // 拡散係数
#define A   1.0     // 初期振幅
#define B   2.0     // 初期周波数パラメータ

int main(void) {
    // ディレクトリ作成
    system("mkdir -p data");
    system("mkdir -p anim");

    // グリッドパラメータ
    double dx = L / (NX - 1);
    double dt = T / (NT - 1);
    double r  = D * dt / (dx * dx);

    // パラメータ表示
    printf("Diffusion equation simulation\n");
    printf("NX=%d, NT=%d, dx=%.6f, dt=%.6f, r=%.6f\n", NX, NT, dx, dt, r);
    if (r > 0.5) {
        printf("Warning: r=%.6f > 0.5, solution may be unstable!\n", r);
    } else {
        printf("Stability condition satisfied: r=%.6f < 0.5\n", r);
    }

    // 解配列
    static double u[NX], u_new[NX];

    // 初期条件：サイン波
    for (int i = 0; i < NX; i++) {
        double x = i * dx;
        u[i] = A * sin(B * M_PI * x / L);
    }

    // 時間ループ
    printf("\nStarting time evolution...\n");
    for (int n = 0; n < NT; n++) {
        if (n % 50 == 0) {
            printf("Step %d/%d (%.1f%%)\n", n, NT-1, 100.0*n/(NT-1));
        }
        // 1) データ出力
        char fname[64];
        sprintf(fname, "data/%04d.txt", n);
        FILE *fp = fopen(fname, "w");
        for (int i = 0; i < NX; i++) {
            fprintf(fp, "%f %f\n", i * dx, u[i]);
        }
        fclose(fp);

        // 2) gnuplot でプロット & PNG 出力
        char cmd[256];
        sprintf(cmd,
            "gnuplot -e \""
            "set terminal png size 800,600; "
            "unset key; "
            "set xrange [0:%f]; "
            "set yrange [-1.2:1.2]; "
            "set xlabel 'x'; "
            "set ylabel 'u(x,t)'; "
            "set title 'Diffusion Equation (t=%.3f)'; "
            "set output 'anim/%04d.png'; "
            "plot '%s' with lines lw 2\"",
            L, n*dt, n, fname
        );
        system(cmd);

        // 3) 次ステップへの更新（陽解法）
        if (n < NT - 1) {
            // 内部点
            for (int i = 1; i < NX - 1; i++) {
                u_new[i] = u[i] + r * (u[i+1] - 2*u[i] + u[i-1]);
            }
            // 境界条件（Dirichlet: u=0）
            u_new[0]      = 0.0;
            u_new[NX - 1] = 0.0;
            // 配列コピー
            for (int i = 0; i < NX; i++) {
                u[i] = u_new[i];
            }
        }
    }

    return 0;
}
