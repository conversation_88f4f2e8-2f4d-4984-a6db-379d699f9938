// 0528.c: 1D Heat Equation Solver with Explicit Method
// Requirements:
// - Use only NX, NT, L, T, D, A, B defined via #define
// - Output NT text files in data/ folder and NT PNG plots in anim/ folder using gnuplot

#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// Global parameters (modify these only)
#define NX  30     // Number of spatial grid points
#define NT  400    // Number of time steps (frames)
#define L   1.0    // Domain length
#define T   1.0    // Total simulation time
#define D   0.1    // Diffusion coefficient
#define A   1.0    // Initial amplitude for sine wave
#define B   2.0    // Frequency parameter for initial sine wave

int main(void) {
    // Create output directories
    system("mkdir -p data");
    system("mkdir -p anim");

    // Grid parameters
    double dx = L / (NX - 1);
    double dt = T / (NT - 1);
    double r  = D * dt / (dx * dx);

    // Stability check
    if (r > 0.5) {
        fprintf(stderr, "Warning: CFL number r=%.6f > 0.5, solution may be unstable!\n", r);
    }

    // Allocate solution arrays (1D only)
    static double u[NX], u_new[NX];

    // Initialize with sine wave: u(x,0) = A * sin(B * pi * x / L)
    for (int i = 0; i < NX; i++) {
        double x = i * dx;
        u[i] = A * sin(B * M_PI * x / L);
    }

    // Time-stepping loop
    for (int n = 0; n < NT; n++) {
        // 1) Write data file: data/n.txt
        char txtfile[64];
        sprintf(txtfile, "data/%d.txt", n);
        FILE *fp = fopen(txtfile, "w");
        for (int i = 0; i < NX; i++) {
            fprintf(fp, "%f %f\n", i * dx, u[i]);
        }
        fclose(fp);

        // 2) Generate PNG plot with gnuplot: anim/n.png
        char cmd[256];
        sprintf(cmd,
            "gnuplot -e \""
            "set terminal png size 800,600;"
            "unset key;"
            "set xrange [0:%f];"
            "set yrange [-1.0:1.0];"
            "set xlabel 'x';"
            "set ylabel 'u(x,t)';"
            "set title sprintf('t=%%.3f', %f * %d / (%d - 1));"
            "set output 'anim/%d.png';"
            "plot '%s' with lines lw 2"\"",
            L, T, n, NT, n, txtfile
        );
        system(cmd);

        // 3) Update solution using explicit finite difference method
        if (n < NT - 1) {
            for (int i = 1; i < NX - 1; i++) {
                u_new[i] = u[i] + r * (u[i+1] - 2 * u[i] + u[i-1]);
            }
            // Apply Dirichlet boundary conditions: u=0 at edges
            u_new[0]      = 0.0;
            u_new[NX-1] = 0.0;
            // Copy new values back to u
            for (int i = 0; i < NX; i++) {
                u[i] = u_new[i];
            }
        }
    }

    return 0;
}
